// backend/src/main.ts
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';
import * as dotenv from 'dotenv';
import { ConfigService } from '@nestjs/config';

dotenv.config(); // Load .env file

async function bootstrap() {
  console.log('Starting application...');
  const app = await NestFactory.create(AppModule);
  console.log('App created successfully');

  const configService = app.get(ConfigService);
  console.log('Config service obtained');

  // Enable CORS
  app.enableCors({
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  // Set global API prefix
  app.setGlobalPrefix('api');

  // Global validation pipe
  app.useGlobalPipes(new ValidationPipe());

  // Get port from environment variables
  const port = configService.get<number>('PORT', 4000);
  console.log(`Attempting to start server on port ${port}...`);

  await app.listen(port);
  console.log(`✅ Application is running on: http://localhost:${port}`);
  console.log(`✅ Health check available at: http://localhost:${port}/api/health`);
}
bootstrap();