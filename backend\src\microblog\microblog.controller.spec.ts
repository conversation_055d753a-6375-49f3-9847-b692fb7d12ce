import { Test, TestingModule } from '@nestjs/testing';
import { MicroblogController } from './microblog.controller';
import { MicroblogService } from './microblog.service';

describe('MicroblogController', () => {
  let controller: MicroblogController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MicroblogController],
      providers: [
        {
          provide: MicroblogService,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<MicroblogController>(MicroblogController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
}); 