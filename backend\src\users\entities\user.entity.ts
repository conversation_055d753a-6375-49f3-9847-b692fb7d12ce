import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, OneToMany, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Product } from '../../products/entities/product.entity';
import { Post } from '../../microblog/entities/post.entity';
import { Comment } from '../../microblog/entities/comment.entity';
import { Like } from '../../microblog/entities/like.entity';
import { Follower } from '../../microblog/entities/follower.entity';
import { ProductImage } from '../../products/entities/product-image.entity';
import { Category } from '../../products/entities/category.entity';
import { JoinColumn } from 'typeorm';


@Entity('user')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ unique: true })
  username!: string;

  @Column({ unique: true })
  email!: string;

  @Column()
  password_hash!: string;

  @Column()
  role!: string;

  @Column({ nullable: true })
  full_name!: string;

  @Column({ nullable: true })
  business_name?: string;

  @Column({ name: 'seller_id', nullable: true })
  sellerId?: string;

  @OneToMany(() => Product, (product) => product.seller)
  products!: Product[];

  @OneToMany(() => Post, (post) => post.author)
  posts!: Post[];

  @OneToMany(() => Comment, (comment) => comment.author)
  comments!: Comment[];

  @OneToMany(() => Like, (like) => like.user)
  likes!: Like[];

  @OneToMany(() => Follower, (follower) => follower.follower)
  following!: Follower[];

  @OneToMany(() => Follower, (follower) => follower.following)
  followers!: Follower[];

  @ManyToOne(() => Category)
  category?: Category;

  @OneToMany(() => ProductImage, (image) => image.product, { cascade: true })
  images?: ProductImage[];

  @ManyToOne(() => User, (user) => user.products)
  @JoinColumn({ name: 'seller_id' }) // Explicitly define the foreign key column
  seller?: User;

  @Column({ nullable: true })
  thumbnailUrl?: string;

  @Column('decimal', { precision: 3, scale: 2, nullable: true, default: 0 })
  rating?: number;

  // Removed duplicate password field - we use password_hash instead

  @Column('int', { default: 0 })
  ratingCount!: number;

  @Column('int', { default: 0 })
  viewCount!: number;

  @Column('int', { default: 0 })
  purchaseCount!: number;

  @Column('int', { default: 0 })
  wishlistCount!: number;

  @Column('int', { default: 0 })
  commentCount!: number;

  @Column('int', { default: 0 })
  shareCount!: number;

  @Column({ default: false })
  is_verified!: boolean;

  @Column({ nullable: true })
  verification_token?: string;

  @Column({ nullable: true })
  reset_token?: string;

  @Column({ nullable: true })
  reset_token_expiry?: Date;

  @Column({ default: 0 })
  failed_login_attempts!: number;

  @Column({ nullable: true })
  last_failed_login?: Date;

  @Column({ default: false })
  is_locked!: boolean;

  @Column({ nullable: true })
  lock_expires_at?: Date;

  @CreateDateColumn()
  created_at!: Date;

  @UpdateDateColumn()
  updated_at!: Date;
}