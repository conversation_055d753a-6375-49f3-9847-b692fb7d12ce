// backend/src/auth/auth.service.ts
import { Injectable, UnauthorizedException, ConflictException, NotFoundException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { User } from '../users/entities/user.entity';
import { RegisterDto } from '../dto/register.dto';
import { LoginDto } from '../dto/login.dto';
import { JwtPayload } from './interfaces/jwt-payload.interface';
import { ConfigService } from '@nestjs/config';
import { MailerService } from '@nestjs-modules/mailer';
import { v4 as uuidv4 } from 'uuid';
import { MoreThan } from 'typeorm';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    private jwtService: JwtService,
    private configService: ConfigService,
    private mailerService: MailerService,
  ) {}

  async register(registerDto: RegisterDto): Promise<{ token: string; user: Partial<User> }> {
    const { username, email, password, role, fullName, businessName } = registerDto;

    // Check if user exists
    const existingUser = await this.usersRepository.findOne({
      where: [{ username }, { email }]
    });
    if (existingUser) {
      throw new ConflictException('Username or email already exists');
    }

    // Hash password
    const salt = await bcrypt.genSalt();
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create new user
    const user = this.usersRepository.create({
      username,
      email,
      password_hash: hashedPassword,
      role,
      full_name: fullName,
      business_name: businessName,
      rating: 0,
      ratingCount: 0,
      viewCount: 0,
      purchaseCount: 0,
      wishlistCount: 0,
      commentCount: 0,
      shareCount: 0,
    });

    await this.usersRepository.save(user);

    // Generate token
    const token = this.generateToken(user);

    // Remove password from response
    const { password_hash, ...result } = user;

    return { token, user: result };
  }

  async login(loginDto: LoginDto): Promise<{ token: string; user: Partial<User> }> {
    const { email, password } = loginDto;

    // Find user
    const user = await this.usersRepository.findOne({ where: { email } });
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Validate password
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Generate token
    const token = this.generateToken(user);

    // Remove password from response
    const { password_hash, ...result } = user;

    return {
      token,
      user: {
        id: result.id,
        email: result.email,
        role: result.role,
        username: result.username,
        full_name: result.full_name,
        business_name: result.business_name
      }
    };
  }

  private generateToken(user: User): string {
    const payload: JwtPayload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    };
    return this.jwtService.sign(payload);
  }

  async getUserById(id: string): Promise<User> {
    const user = await this.usersRepository.findOne({ where: { id } });
    if (!user) {
      throw new UnauthorizedException('User not found');
    }
    return user;
  }

  async forgotPassword(email: string): Promise<void> {
    const user = await this.usersRepository.findOne({ where: { email } });
    if (!user) {
      // Don't reveal if user exists
      return;
    }

    const resetToken = uuidv4();
    const resetTokenExpiry = new Date();
    resetTokenExpiry.setHours(resetTokenExpiry.getHours() + 1); // Token valid for 1 hour

    await this.usersRepository.update(user.id, {
      reset_token: resetToken,
      reset_token_expiry: resetTokenExpiry,
    });

    const resetUrl = `${this.configService.get('FRONTEND_URL')}/reset-password?token=${resetToken}`;
    
    await this.mailerService.sendMail({
      to: email,
      subject: 'Password Reset Request',
      template: 'password-reset',
      context: {
        name: user.username,
        resetUrl,
      },
    });
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    const user = await this.usersRepository.findOne({
      where: {
        reset_token: token,
        reset_token_expiry: MoreThan(new Date()),
      },
    });

    if (!user) {
      throw new NotFoundException('Invalid or expired reset token');
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);

    await this.usersRepository.update(user.id, {
      password_hash: hashedPassword,
      reset_token: undefined,
      reset_token_expiry: undefined,
    });
  }

  async verifyEmail(token: string): Promise<void> {
    const user = await this.usersRepository.findOne({
      where: {
        verification_token: token,
      },
    });

    if (!user) {
      throw new NotFoundException('Invalid verification token');
    }

    await this.usersRepository.update(user.id, {
      is_verified: true,
      verification_token: undefined,
    });
  }

  async resendVerificationEmail(userId: string): Promise<void> {
    const user = await this.usersRepository.findOne({ where: { id: userId } });
    if (!user || user.is_verified) {
      return;
    }

    const verificationToken = uuidv4();
    await this.usersRepository.update(user.id, {
      verification_token: verificationToken,
    });

    const verificationUrl = `${this.configService.get('FRONTEND_URL')}/verify-email?token=${verificationToken}`;
    
    await this.mailerService.sendMail({
      to: user.email,
      subject: 'Verify Your Email',
      template: 'email-verification',
      context: {
        name: user.username,
        verificationUrl,
      },
    });
  }
}