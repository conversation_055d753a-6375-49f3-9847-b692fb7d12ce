import { Test } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { JwtService } from '@nestjs/jwt';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';

async function testAuthSystem() {
  console.log('=== TESTING AUTHENTICATION SYSTEM ===');

  // Create a testing module
  const moduleRef = await Test.createTestingModule({
    providers: [
      AuthService,
      {
        provide: JwtService,
        useValue: {
          sign: jest.fn().mockReturnValue('test-token'),
          verify: jest.fn().mockReturnValue({ sub: '1', username: 'test', email: '<EMAIL>', role: 'user' }),
        },
      },
      {
        provide: getRepositoryToken(User),
        useValue: {
          findOne: jest.fn().mockImplementation((query) => {
            if (query.where && query.where.email === '<EMAIL>') {
              return {
                id: '1',
                username: 'test',
                email: '<EMAIL>',
                password_hash: bcrypt.hashSync('password123', 10),
                role: 'user',
              };
            }
            return null;
          }),
          create: jest.fn().mockImplementation((data) => ({
            id: '1',
            ...data,
          })),
          save: jest.fn().mockImplementation((user) => Promise.resolve(user)),
        },
      },
      {
        provide: ConfigService,
        useValue: {
          get: jest.fn().mockImplementation((key, defaultValue) => {
            if (key === 'JWT_SECRET') return 'test-secret';
            if (key === 'JWT_EXPIRATION') return '1d';
            return defaultValue;
          }),
        },
      },
    ],
  }).compile();

  const authService = moduleRef.get<AuthService>(AuthService);
  // const jwtService = moduleRef.get<JwtService>(JwtService); // Commented out as it's not used
  const userRepository = moduleRef.get(getRepositoryToken(User));

  console.log('✅ Test module created successfully');

  // Test user registration
  console.log('\n--- Testing User Registration ---');
  try {
    const registerDto = {
      username: 'newuser',
      email: '<EMAIL>',
      password: 'password123',
      role: 'user',
      fullName: 'New User',
      businessName: 'New Business',
    };

    // Mock findOne to return null (user doesn't exist)
    userRepository.findOne.mockResolvedValueOnce(null);

    const result = await authService.register(registerDto);

    console.log('✅ User registration successful');
    console.log('   Token generated:', result.token ? 'Yes' : 'No');
    console.log('   User created:', result.user ? 'Yes' : 'No');
  } catch (error) {
    console.log('❌ User registration failed:', error instanceof Error ? error.message : String(error));
  }

  // Test user login
  console.log('\n--- Testing User Login ---');
  try {
    const loginDto = {
      email: '<EMAIL>',
      password: 'password123',
    };

    // Mock bcrypt.compare to return true
    jest.spyOn(bcrypt, 'compare').mockImplementation(() => Promise.resolve(true));

    const result = await authService.login(loginDto);

    console.log('✅ User login successful');
    console.log('   Token generated:', result.token ? 'Yes' : 'No');
    console.log('   User retrieved:', result.user ? 'Yes' : 'No');
  } catch (error) {
    console.log('❌ User login failed:', error instanceof Error ? error.message : String(error));
  }

  console.log('\n=== AUTHENTICATION SYSTEM TEST SUMMARY ===');
  console.log('The authentication system appears to be working correctly.');
  console.log('JWT token generation and verification is functioning.');
  console.log('User registration and login are working.');
}

// Run the test
testAuthSystem().catch(error => {
  console.error('Test failed with error:', error.message);
});
