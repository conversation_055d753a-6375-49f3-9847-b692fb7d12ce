'use client';

import { Store, MessageSquare, CreditCard } from 'lucide-react';

export default function Features() {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Key Features
          </h2>
          <p className="text-xl text-gray-600">
            Discover what makes Market O&apos;Clock the ultimate B2B2C marketplace platform.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="flex flex-col items-center text-center p-6 rounded-lg bg-gray-50">
            <div className="p-3 bg-indigo-100 rounded-full mb-4">
              <Store className="h-8 w-8 text-indigo-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Marketplace</h3>
            <p className="text-gray-600">
              Connect suppliers with retailers through our efficient marketplace platform.
            </p>
          </div>
          <div className="flex flex-col items-center text-center p-6 rounded-lg bg-gray-50">
            <div className="p-3 bg-indigo-100 rounded-full mb-4">
              <MessageSquare className="h-8 w-8 text-indigo-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Microblogs & Blogs</h3>
            <p className="text-gray-600">
              Market products through engaging content with social features.
            </p>
          </div>
          <div className="flex flex-col items-center text-center p-6 rounded-lg bg-gray-50">
            <div className="p-3 bg-indigo-100 rounded-full mb-4">
              <CreditCard className="h-8 w-8 text-indigo-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Multi-Payment Support</h3>
            <p className="text-gray-600">
              Secure and flexible payment options for all transactions.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
