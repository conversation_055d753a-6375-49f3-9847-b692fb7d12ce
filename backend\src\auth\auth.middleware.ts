import { Injectable, NestMiddleware } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  async use(req: Request, _res: Response, next: NextFunction) {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        // No token provided, but we'll let the request continue
        // Protected routes should use the JwtAuthGuard
        return next();
      }

      const token = authHeader.split(' ')[1];
      const secret = this.configService.get<string>('JWT_SECRET') || 'default_jwt_secret_for_development';

      try {
        const decoded = this.jwtService.verify(token, { secret });
        req['user'] = decoded;
      } catch (error) {
        // Invalid token, but we'll let the request continue
        // Protected routes should use the JwtAuthGuard
      }

      next();
    } catch (error) {
      next();
    }
  }
}
