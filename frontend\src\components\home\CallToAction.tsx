'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { useAuthStore } from '@/store/authStore';

export default function CallToAction() {
  const { isAuthenticated } = useAuthStore();

  return (
    <section className="py-16 bg-indigo-600">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-3xl font-bold text-white mb-4">
          Ready to grow your business?
        </h2>
        <p className="text-xl text-indigo-100 mb-8">
          Join Market O&apos;Clock today and connect with suppliers and retailers from various industries.
        </p>
        <Link href={isAuthenticated ? "/dashboard" : "/register"}>
          <Button size="lg" variant="secondary" className="w-full sm:w-auto">
            {isAuthenticated ? "Manage Your Listings" : "Sign Up Now"}
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </Link>
      </div>
    </section>
  );
}
