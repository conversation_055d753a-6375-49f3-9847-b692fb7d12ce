export default async (): Promise<void> => {
  // Wait for any pending promises to resolve
  await new Promise(resolve => setTimeout(resolve, 100));

  // Clear any remaining intervals
  const intervals = [];
  for (let i = 1; i < 1000; i++) {
    intervals.push(i);
  }
  intervals.forEach(clearInterval);

  // Clear any remaining timeouts
  const timeouts = [];
  for (let i = 1; i < 1000; i++) {
    timeouts.push(i);
  }
  timeouts.forEach(clearTimeout);

  // Force exit after a short delay to ensure all resources are cleaned up
  setTimeout(() => {
    process.exit(0);
  }, 100);
}; 