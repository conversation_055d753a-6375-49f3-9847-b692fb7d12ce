import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Post } from '../../microblog/entities/post.entity';

@Entity('notifications')
export class Notification {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  type!: string; // 'like', 'comment', 'follow', 'post'

  @Column()
  message!: string;

  @Column({ default: false })
  isRead!: boolean;

  @ManyToOne(() => User)
  recipient?: User;

  @ManyToOne(() => User)
  actor?: User; // User who performed the action

  @ManyToOne(() => Post, { nullable: true })
  post?: Post; // Related post if applicable

  @CreateDateColumn()
  createdAt!: Date;
}