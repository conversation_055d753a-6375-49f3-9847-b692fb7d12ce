import { Test, TestingModule } from '@nestjs/testing';
import { MicroblogService } from './microblog.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Post } from './entities/post.entity';
import { Comment } from './entities/comment.entity';
import { Like } from './entities/like.entity';

const mockPostRepository = {};
const mockCommentRepository = {};
const mockLikeRepository = {};

describe('MicroblogService', () => {
  let service: MicroblogService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MicroblogService,
        {
          provide: getRepositoryToken(Post),
          useValue: mockPostRepository,
        },
        {
          provide: getRepositoryToken(Comment),
          useValue: mockCommentRepository,
        },
        {
          provide: getRepositoryToken(Like),
          useValue: mockLikeRepository,
        },
      ],
    }).compile();

    service = module.get<MicroblogService>(MicroblogService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
}); 