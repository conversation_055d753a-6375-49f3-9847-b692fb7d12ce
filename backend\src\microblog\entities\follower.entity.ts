import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, ManyToOne, CreateDateColumn, Unique } from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('followers')
@Unique(['follower', 'following'])
export class Follower {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => User, user => user.following)
  follower?: User;

  @ManyToOne(() => User, user => user.followers)
  following?: User;

  @CreateDateColumn()
  createdAt?: Date;
}
