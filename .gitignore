# dependencies
node_modules/
.pnp/
.pnp.js

# testing
/coverage
/.nyc_output

# next.js
.next/
out/

# production
build/
dist/

# environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store
Thumbs.db

# IDE
.idea/
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# temp files
.temp
.tmp