// app/microblog/page.tsx
'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import axios from 'axios';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { useAuthStore } from '@/store/authStore';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api';

interface MicroblogPost {
  id: string;
  content: string;
  imageUrl?: string;
  likesCount: number;
  commentsCount: number;
  createdAt: string;
  user: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
}

export default function MicroblogPage() {
  const [posts, setPosts] = useState<MicroblogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated } = useAuthStore();

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`${API_URL}/microblog/feed`);
        setPosts(response.data.data || []);
      } catch (err) {
        console.error('Error fetching posts:', err);
        setError('Failed to load posts');
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading posts...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6 text-center">Market Talk</h1>

      {!isAuthenticated && (
        <div className="text-center mb-6 p-4 bg-muted rounded-lg">
          <p>Please log in to see posts and interact with the community.</p>
        </div>
      )}

      <div className="space-y-6">
        {posts.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No posts yet. Be the first to share something!</p>
          </div>
        ) : (
          posts.map((post) => (
            <Card key={post.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="font-bold text-lg">
                      {post.user.firstName && post.user.lastName
                        ? `${post.user.firstName} ${post.user.lastName}`
                        : post.user.email.split('@')[0]
                      }
                    </span>
                    <p className="text-sm text-muted-foreground">
                      {new Date(post.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {post.likesCount} Likes
                  </span>
                </div>
              </CardHeader>
              <CardContent>
                <p className="mb-4">{post.content}</p>
                {post.imageUrl && (
                  <div className="mb-4">
                    <Image
                      src={post.imageUrl}
                      alt={post.content}
                      width={800}
                      height={600}
                      className="rounded-md w-full h-auto object-cover"
                    />
                  </div>
                )}
                <div className="flex gap-4">
                  <Button variant="outline" size="sm" disabled={!isAuthenticated}>
                    Like ({post.likesCount})
                  </Button>
                  <Button variant="outline" size="sm" disabled={!isAuthenticated}>
                    Comment ({post.commentsCount})
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
