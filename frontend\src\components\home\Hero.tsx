'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { useAuthStore } from '@/store/authStore';

export default function Hero() {
  const { isAuthenticated } = useAuthStore();

  return (
    <section className="relative bg-gray-900 text-white">
      <div className="absolute inset-0">
        <Image
          src="/images/hero-bg.jpg"
          alt="Hero background"
          fill
          className="object-cover opacity-50"
          priority
        />
      </div>
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32">
        <div className="max-w-3xl">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Connect, Market, Grow Your Business
          </h1>
          <p className="text-xl text-gray-300 mb-8">
            Market O&apos;Clock is a B2B2C marketplace that connects suppliers with retailers and businesses through innovative social engagement features.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Link href={isAuthenticated ? "/dashboard" : "/register"}>
              <Button size="lg" className="w-full sm:w-auto">
                {isAuthenticated ? "Go to Dashboard" : "Get Started"}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link href="/marketplace">
              <Button size="lg" variant="outline" className="w-full sm:w-auto">
                Explore Marketplace
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
