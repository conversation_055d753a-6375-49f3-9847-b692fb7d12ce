import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(configService: ConfigService) {
    const jwtConfig = {
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: configService.get('JWT_IGNORE_EXPIRATION') === 'true',
      secretOrKey: configService.get<string>('JWT_SECRET') || 'default-secret',
    };
    super(jwtConfig);
  }

  async validate(payload: { sub: string; email: string; role: string; username: string }) {
    return {
      id: payload.sub,
      email: payload.email,
      role: payload.role,
      username: payload.username
    };
  }
}