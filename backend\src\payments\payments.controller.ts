import { Controller, Post, Body, Param } from '@nestjs/common';
import { MockPaymentService } from './mock-payment.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('payments')
@Controller('payments')
export class PaymentsController {
  constructor(private readonly mockPaymentService: MockPaymentService) {}

  @Post('create-intent')
  @ApiOperation({ summary: 'Create a payment intent' })
  @ApiResponse({ status: 201, description: 'Payment intent created successfully' })
  async createPaymentIntent(@Body() { amount, currency }: { amount: number; currency?: string }) {
    return this.mockPaymentService.createPaymentIntent(amount, currency);
  }

  @Post('confirm/:id')
  @ApiOperation({ summary: 'Confirm a payment intent' })
  @ApiResponse({ status: 200, description: 'Payment confirmed successfully' })
  async confirmPaymentIntent(@Param('id') paymentIntentId: string) {
    return this.mockPaymentService.confirmPaymentIntent(paymentIntentId);
  }

  @Post('refund/:id')
  @ApiOperation({ summary: 'Refund a payment' })
  @ApiResponse({ status: 200, description: 'Refund processed successfully' })
  async refundPayment(@Param('id') paymentIntentId: string, @Body() { amount }: { amount?: number }) {
    return this.mockPaymentService.refundPayment(paymentIntentId, amount);
  }
}