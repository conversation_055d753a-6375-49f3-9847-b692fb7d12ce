{"name": "marketoclock", "version": "1.0.0", "description": "Market-o-Clock - A comprehensive market analysis and trading platform", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"pnpm --filter market-o-clock-backend dev\" \"pnpm --filter market-oclock dev\"", "dev:frontend": "pnpm --filter market-oclock dev", "dev:backend": "pnpm --filter market-o-clock-backend dev", "build": "pnpm --filter market-o-clock-backend build && pnpm --filter market-oclock build", "build:frontend": "pnpm --filter market-oclock build", "build:backend": "pnpm --filter market-o-clock-backend build", "start": "concurrently \"pnpm --filter market-o-clock-backend start\" \"pnpm --filter market-oclock start\"", "start:frontend": "pnpm --filter market-oclock start", "start:backend": "pnpm --filter market-o-clock-backend start", "lint": "pnpm --filter market-oclock lint && pnpm --filter market-o-clock-backend lint", "lint:frontend": "pnpm --filter market-oclock lint", "lint:backend": "pnpm --filter market-o-clock-backend lint", "test": "pnpm --filter market-o-clock-backend test && pnpm --filter market-oclock test", "test:frontend": "pnpm --filter market-oclock test", "test:backend": "pnpm --filter market-o-clock-backend test", "clean": "rimraf node_modules frontend/node_modules backend/node_modules", "install:all": "pnpm install", "docker:build": "docker-compose -f backend/docker-compose.yml build", "docker:up": "docker-compose -f backend/docker-compose.yml up -d", "docker:down": "docker-compose -f backend/docker-compose.yml down"}, "devDependencies": {"concurrently": "^9.1.0", "rimraf": "^6.0.1", "@types/node": "^20.17.28", "typescript": "^5.4.2", "eslint": "^9.0.0", "prettier": "^3.2.5"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@9.1.0", "pnpm": {"onlyBuiltDependencies": ["@nestjs/core", "aws-sdk", "bcrypt"]}, "keywords": ["market-analysis", "trading", "analytics", "nextjs", "<PERSON><PERSON><PERSON>", "typescript", "monorepo"], "author": "Pkowech", "license": "MIT"}