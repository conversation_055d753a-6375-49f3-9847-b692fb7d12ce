import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { AuthService } from '../src/auth/auth.service';
import { UsersService } from '../src/users/users.service';
import { ProductsService } from '../src/products/products.service';
import { CategoriesService } from '../src/categories/categories.service';
import { SocialService } from '../src/mongodb/services/social.service';

describe('Application Features (e2e)', () => {
  let app: INestApplication;
  let authService: AuthService;
  let usersService: UsersService;
  let productsService: ProductsService;
  let categoriesService: CategoriesService;
  let socialService: SocialService;
  let authToken: string;
  let testUserId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    authService = moduleFixture.get<AuthService>(AuthService);
    usersService = moduleFixture.get<UsersService>(UsersService);
    productsService = moduleFixture.get<ProductsService>(ProductsService);
    categoriesService = moduleFixture.get<CategoriesService>(CategoriesService);
    socialService = moduleFixture.get<SocialService>(SocialService);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Authentication System', () => {
    it('should register a new user', async () => {
      const registerDto = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        role: 'user',
        fullName: 'Test User',
        businessName: 'Test Business'
      };

      const response = await request(app.getHttpServer())
        .post('/auth/register')
        .send(registerDto)
        .expect(201);

      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
      authToken = response.body.token;
      testUserId = response.body.user.id;
    });

    it('should login with registered user', async () => {
      const loginDto = {
        email: '<EMAIL>',
        password: 'TestPassword123!'
      };

      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send(loginDto)
        .expect(200);

      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
    });
  });

  describe('User Management', () => {
    it('should get user profile', async () => {
      const response = await request(app.getHttpServer())
        .get(`/users/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('username');
      expect(response.body).toHaveProperty('email');
    });

    it('should update user profile', async () => {
      const updateDto = {
        fullName: 'Updated Test User',
        businessName: 'Updated Test Business'
      };

      const response = await request(app.getHttpServer())
        .patch(`/users/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateDto)
        .expect(200);

      expect(response.body.fullName).toBe(updateDto.fullName);
      expect(response.body.businessName).toBe(updateDto.businessName);
    });
  });

  describe('Products and Categories', () => {
    let categoryId: string;
    let productId: string;

    it('should create a category', async () => {
      const categoryDto = {
        name: 'Test Category',
        description: 'Test Category Description'
      };

      const response = await request(app.getHttpServer())
        .post('/categories')
        .set('Authorization', `Bearer ${authToken}`)
        .send(categoryDto)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      categoryId = response.body.id;
    });

    it('should create a product', async () => {
      const productDto = {
        name: 'Test Product',
        description: 'Test Product Description',
        price: 99.99,
        categoryId: categoryId,
        stock: 100
      };

      const response = await request(app.getHttpServer())
        .post('/products')
        .set('Authorization', `Bearer ${authToken}`)
        .send(productDto)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      productId = response.body.id;
    });

    it('should get product details', async () => {
      const response = await request(app.getHttpServer())
        .get(`/products/${productId}`)
        .expect(200);

      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('name');
      expect(response.body).toHaveProperty('price');
    });
  });

  describe('Social Features', () => {
    let postId: string;

    it('should create a social post', async () => {
      const postDto = {
        content: 'Test post content',
        mediaUrls: ['https://example.com/image.jpg']
      };

      const response = await request(app.getHttpServer())
        .post('/social/posts')
        .set('Authorization', `Bearer ${authToken}`)
        .send(postDto)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      postId = response.body.id;
    });

    it('should like a post', async () => {
      const response = await request(app.getHttpServer())
        .post(`/social/posts/${postId}/like`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success');
    });

    it('should add a comment to a post', async () => {
      const commentDto = {
        content: 'Test comment'
      };

      const response = await request(app.getHttpServer())
        .post(`/social/posts/${postId}/comments`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(commentDto)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.content).toBe(commentDto.content);
    });
  });

  describe('API Health and Status', () => {
    it('should check API health', async () => {
      const response = await request(app.getHttpServer())
        .get('/health')
        .expect(200);

      expect(response.text).toBe('OK');
    });

    it('should get API status', async () => {
      const response = await request(app.getHttpServer())
        .get('/status')
        .expect(200);

      expect(response.text).toBe('Service is running');
    });
  });
}); 