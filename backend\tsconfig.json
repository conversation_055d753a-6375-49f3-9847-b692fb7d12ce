{"compilerOptions": {"target": "es2016", "module": "commonjs", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "outDir": "./dist", "rootDir": "./src", "declaration": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.test.ts", "dist"]}