// backend/src/payments/mock-payment.service.ts
import { Injectable } from '@nestjs/common';

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: 'requires_payment_method' | 'requires_confirmation' | 'requires_action' | 'processing' | 'requires_capture' | 'canceled' | 'succeeded';
  client_secret: string;
  created: number;
}

@Injectable()
export class MockPaymentService {
  async createPaymentIntent(amount: number, currency: string = 'usd'): Promise<PaymentIntent> {
    // Mock payment intent creation
    const paymentIntent: PaymentIntent = {
      id: `pi_mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      amount,
      currency,
      status: 'requires_payment_method',
      client_secret: `pi_mock_${Date.now()}_secret_${Math.random().toString(36).substr(2, 9)}`,
      created: Math.floor(Date.now() / 1000),
    };

    console.log('Mock Payment Intent Created:', paymentIntent);
    return paymentIntent;
  }

  async confirmPaymentIntent(paymentIntentId: string): Promise<PaymentIntent> {
    // Mock payment confirmation
    const confirmedPayment: PaymentIntent = {
      id: paymentIntentId,
      amount: 1000, // Mock amount
      currency: 'usd',
      status: 'succeeded',
      client_secret: `${paymentIntentId}_secret`,
      created: Math.floor(Date.now() / 1000),
    };

    console.log('Mock Payment Confirmed:', confirmedPayment);
    return confirmedPayment;
  }

  async refundPayment(paymentIntentId: string, amount?: number): Promise<{ id: string; status: string; amount: number }> {
    // Mock refund
    const refund = {
      id: `re_mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      status: 'succeeded',
      amount: amount || 1000,
      paymentIntentId, // Include the payment intent ID for reference
    };

    console.log('Mock Refund Created for Payment Intent:', paymentIntentId, refund);
    return refund;
  }
}