'use client';

import React, { useState } from 'react';
import Image, { ImageProps } from 'next/image';
import { getPlaceholderImage, PLACEHOLDER_IMAGES, createSafeImageUrl } from '@/lib/placeholders';
import { cn } from '@/lib/utils';

interface SafeImageProps extends Omit<ImageProps, 'src' | 'onError'> {
  src: string | null | undefined;
  fallbackType?: keyof typeof PLACEHOLDER_IMAGES;
  showLoadingState?: boolean;
  className?: string;
}

/**
 * SafeImage component that automatically handles broken images with fallbacks
 */
export function SafeImage({
  src,
  alt,
  fallbackType = 'fallback',
  showLoadingState = false,
  className,
  width,
  height,
  ...props
}: SafeImageProps) {
  const [isLoading, setIsLoading] = useState(showLoadingState);
  const [hasError, setHasError] = useState(false);
  
  // Determine dimensions for placeholder
  const imgWidth = typeof width === 'number' ? width : 400;
  const imgHeight = typeof height === 'number' ? height : 400;
  
  // Create safe image URL
  const safeImageUrl = createSafeImageUrl(src, fallbackType, imgWidth, imgHeight);
  
  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleError = () => {
    setIsLoading(false);
    if (!hasError) {
      setHasError(true);
    }
  };

  // If there's an error and we're not already showing a placeholder, show placeholder
  const finalSrc = hasError && !safeImageUrl.includes('unsplash.com') && !safeImageUrl.includes('ui-avatars.com')
    ? getPlaceholderImage(fallbackType, imgWidth, imgHeight)
    : safeImageUrl;

  return (
    <div className={cn('relative', className)}>
      {isLoading && showLoadingState && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded" />
      )}
      <Image
        src={finalSrc}
        alt={alt}
        width={width}
        height={height}
        onLoad={handleLoad}
        onError={handleError}
        className={cn(
          'transition-opacity duration-200',
          isLoading ? 'opacity-0' : 'opacity-100'
        )}
        {...props}
      />
    </div>
  );
}

/**
 * SafeAvatar component specifically for user avatars
 */
interface SafeAvatarProps extends Omit<SafeImageProps, 'fallbackType'> {
  name?: string;
  size?: number;
}

export function SafeAvatar({
  src,
  name,
  size = 40,
  alt,
  className,
  ...props
}: SafeAvatarProps) {
  const [hasError, setHasError] = useState(false);
  
  const handleError = () => {
    setHasError(true);
  };

  // If we have a name and no valid src, use text placeholder
  const finalSrc = (!src || hasError) && name
    ? `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&size=${size}&background=6366f1&color=ffffff&bold=true`
    : createSafeImageUrl(src, 'avatar', size, size);

  return (
    <div className={cn('relative overflow-hidden rounded-full', className)}>
      <Image
        src={finalSrc}
        alt={alt || name || 'Avatar'}
        width={size}
        height={size}
        onError={handleError}
        className="object-cover"
        {...props}
      />
    </div>
  );
}

/**
 * SafeProductImage component specifically for product images
 */
interface SafeProductImageProps extends Omit<SafeImageProps, 'fallbackType'> {
  productName?: string;
}

export function SafeProductImage({
  src,
  productName,
  alt,
  ...props
}: SafeProductImageProps) {
  return (
    <SafeImage
      src={src}
      alt={alt || productName || 'Product image'}
      fallbackType="product"
      {...props}
    />
  );
}
