// Placeholder image utilities
export const PLACEHOLDER_IMAGES = {
  // Product placeholders
  product: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop&crop=center',
  productLarge: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=600&fit=crop&crop=center',
  
  // User/Avatar placeholders
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
  
  // Hero/Background placeholders
  hero: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=1920&h=1080&fit=crop&crop=center',
  
  // Category placeholders
  electronics: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400&h=400&fit=crop&crop=center',
  fashion: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=400&fit=crop&crop=center',
  home: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop&crop=center',
  books: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=400&fit=crop&crop=center',
  
  // Generic fallback
  fallback: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop&crop=center',
};

/**
 * Get a placeholder image URL based on type and dimensions
 */
export function getPlaceholderImage(
  type: keyof typeof PLACEHOLDER_IMAGES = 'fallback',
  width = 400,
  height = 400
): string {
  const baseUrl = PLACEHOLDER_IMAGES[type] || PLACEHOLDER_IMAGES.fallback;
  
  // If it's already a full URL with dimensions, return as is
  if (baseUrl.includes('w=') && baseUrl.includes('h=')) {
    return baseUrl.replace(/w=\d+/, `w=${width}`).replace(/h=\d+/, `h=${height}`);
  }
  
  return `${baseUrl}?w=${width}&h=${height}&fit=crop&crop=center`;
}

/**
 * Generate a placeholder based on text (for initials, etc.)
 */
export function getTextPlaceholder(
  text: string,
  width = 400,
  height = 400,
  bgColor = '6366f1',
  textColor = 'ffffff'
): string {
  const initials = text
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
    
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&size=${width}&background=${bgColor}&color=${textColor}&bold=true`;
}

/**
 * Handle image error by setting a fallback
 */
export function handleImageError(
  event: React.SyntheticEvent<HTMLImageElement>,
  fallbackType: keyof typeof PLACEHOLDER_IMAGES = 'fallback',
  width = 400,
  height = 400
) {
  const img = event.currentTarget;
  if (img.src !== getPlaceholderImage(fallbackType, width, height)) {
    img.src = getPlaceholderImage(fallbackType, width, height);
  }
}

/**
 * Create a safe image URL with fallback
 */
export function createSafeImageUrl(
  url: string | null | undefined,
  fallbackType: keyof typeof PLACEHOLDER_IMAGES = 'fallback',
  width = 400,
  height = 400
): string {
  if (!url || url.includes('example.com') || url.includes('/api/placeholder/')) {
    return getPlaceholderImage(fallbackType, width, height);
  }
  return url;
}
