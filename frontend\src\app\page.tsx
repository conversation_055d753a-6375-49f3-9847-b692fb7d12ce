// frontend/src/app/page.tsx
'use client';

import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';
import axios from 'axios';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuthStore } from '@/store/authStore';
import HeroSection from '@/components/home/<USER>';
import { CallToAction, Features } from '@/components/home';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api';

interface Post {
  id: string;
  content: string;
}

export default function Home() {
  const { isAuthenticated, checkAuth } = useAuthStore();
  const [posts, setPosts] = useState<Post[]>([]);

  useEffect(() => {
    checkAuth();
    axios
      .get<Post[]>(`${API_URL}/posts?limit=3`)
      .then((res) => setPosts(res.data))
      .catch((err) => console.error('Error fetching posts:', err));
  }, [checkAuth]);

  return (
    <div className="min-h-screen bg-background">
      <Head>
        <title>Market O'Clock | B2B2C Marketplace</title>
        <meta name="description" content="Connecting suppliers with retailers for a seamless B2B2C experience" />
        <meta property="og:title" content="Market O'Clock" />
        <meta property="og:description" content="Join the future of B2B2C commerce" />
        <meta property="og:image" content="/images/hero-bg.jpg" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main>
        <main className="container py-12">
        <HeroSection />
        <Features />
        <CallToAction/>
      </main>

        {/* Featured Products Section */}
        <section className="py-12 bg-gray-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-8">Featured Products</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                { src: 'https://images.unsplash.com/photo-1529374255404-311a2a4f1fd9', title: 'Product 1', desc: 'Amazing product on sale!', sale: '$80.00' },
                { src: 'https://images.unsplash.com/photo-1542272604-787c3835535d', title: 'Product 2', desc: "Don't miss this deal!", sale: '$40.00' },
                { src: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43', title: 'Product 3', desc: 'Limited time offer!', sale: '$60.00' },
              ].map((product) => (
                <Card key={product.title} className="hover:shadow-lg transition-shadow">
                  <Image src={product.src} alt={product.title} width={300} height={200} className="rounded-t-md object-cover" />
                  <CardHeader>
                    <CardTitle>{product.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-2">{product.desc}</p>
                    <p className="text-indigo-600 font-bold text-lg">{product.sale}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="text-center mt-8">
              <Button variant="outline" size="lg" asChild>
                <Link href="/marketplace">View All Products</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}