import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { AuthService } from './auth.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const authService = app.get(AuthService);

  try {
    // Test registration
    const registerResult = await authService.register({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
      role: 'user',
      fullName: 'Test User',
      businessName: 'Test Business',
    });
    console.log('Registration successful:', registerResult);

    // Test login
    const loginResult = await authService.login({
      email: '<EMAIL>',
      password: 'password123',
    });
    console.log('Login successful:', loginResult);

    // Test getting user by ID
    if (loginResult.user.id) {
      const user = await authService.getUserById(loginResult.user.id);
      console.log('User retrieved:', user);
    }

  } catch (error) {
    console.error('Error:', error instanceof Error ? error.message : String(error));
  } finally {
    await app.close();
  }
}

bootstrap();
