// Simple test script to verify authentication functionality
console.log('Authentication system test');
console.log('==========================');

// Test JWT token generation
const jwt = require('jsonwebtoken');
const secret = process.env.JWT_SECRET || 'default_jwt_secret_for_development';

try {
  // Generate a test token
  const payload = {
    sub: '1',
    username: 'testuser',
    email: '<EMAIL>',
    role: 'user'
  };
  
  const token = jwt.sign(payload, secret, { expiresIn: '1d' });
  console.log('JWT token generation: SUCCESS');
  console.log('Token:', token);
  
  // Verify the token
  const decoded = jwt.verify(token, secret);
  console.log('JWT token verification: SUCCESS');
  console.log('Decoded token:', decoded);
  
  console.log('\nAuthentication system is working correctly!');
} catch (error) {
  console.error('Authentication test failed:', error.message);
}
